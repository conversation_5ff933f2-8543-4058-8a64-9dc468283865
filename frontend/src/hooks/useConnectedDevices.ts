/**
 * Hook لإدارة بيانات الأجهزة المتصلة بالشبكة في الوقت الفعلي
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import api from '../lib/axios';

export interface ConnectedDevice {
  device_id: string;
  client_ip: string;
  hostname: string;
  platform: string;
  system: string;
  is_main_server: boolean;
  is_local_access: boolean;
  device_type: string;
  first_access: string;
  last_access: string;
  access_count: number;
  user_agent?: string;
  current_user?: string;
  status: 'online' | 'recently_active' | 'offline' | 'pending_approval' | 'approved_online' | 'approved_offline' | 'blocked';
  requires_approval?: boolean;
  is_approved?: boolean;  // ✅ إضافة خاصية is_approved
  approval_status?: 'pending' | 'approved' | 'not_required' | 'blocked';
  // معلومات الموافقة
  approved_by?: string;
  approved_at?: string;
  approval_notes?: string;
  // معلومات الحظر
  blocked_by?: string;
  blocked_at?: string;
  block_reason?: string;
  original_table?: string;
  // معلومات البصمة المتقدمة
  is_advanced_fingerprint?: boolean;
  hardware_fingerprint?: string;
  storage_fingerprint?: string;
  screen_fingerprint?: string;
  system_fingerprint?: string;
  machine?: string;
  browser?: string;
}

export interface DevicesSummary {
  total_devices: number;
  main_server_count: number;
  local_devices_count: number;
  remote_devices_count: number;
  online_devices: number;
  recently_active_devices: number;
  offline_devices: number;
  pending_approval_devices: number;
  // إحصائيات الأمان
  blocked_devices_count: number;
  pending_devices_count: number;
  approved_devices_count: number;
  // ✅ إضافة إحصائيات البصمات المتقدمة
  advanced_fingerprint_count?: number;
}

export interface ConnectedDevicesResult {
  devices: ConnectedDevice[];
  summary: DevicesSummary;
  isLoading: boolean;
  error?: string;
  currentClientIp?: string;
  lastUpdated?: Date;
}

// دالة مساعدة لفحص الأجهزة المعتمدة
const isApprovedDevice = (device: ConnectedDevice): boolean => {
  return !!(device.approved_by || device.approved_at || device.requires_approval === false);
};

/**
 * Hook للحصول على الأجهزة المتصلة وإدارتها في الوقت الفعلي
 */
export const useConnectedDevices = (useRealTime: boolean = false) => {
  const [devicesData, setDevicesData] = useState<ConnectedDevicesResult>({
    devices: [],
    summary: {
      total_devices: 0,
      main_server_count: 0,
      local_devices_count: 0,
      remote_devices_count: 0,
      online_devices: 0,
      recently_active_devices: 0,
      offline_devices: 0,
      pending_approval_devices: 0,
      blocked_devices_count: 0,
      pending_devices_count: 0,
      approved_devices_count: 0,
      advanced_fingerprint_count: 0,
    },
    isLoading: true,
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef<number>(0);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const maxReconnectAttempts = 3;
  const lastFetchRef = useRef<number>(0);
  // Cache للأجهزة المعتمدة لضمان عدم اختفائها
  const approvedDevicesCache = useRef<Map<string, ConnectedDevice>>(new Map());
  const minFetchInterval = 1000; // ثانية واحدة لتحديث أسرع
  const forceUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const dataValidityRef = useRef<number>(0); // للتحقق من صحة البيانات
  const maxDataAge = 30000; // عمر البيانات الأقصى: 30 ثانية

  const fetchConnectedDevices = useCallback(async () => {
    try {
      // منع الطلبات المتكررة مع فحص صحة البيانات
      const now = Date.now();
      if (now - lastFetchRef.current < minFetchInterval) {
        return;
      }

      // فحص عمر البيانات الحالية
      const dataAge = now - dataValidityRef.current;
      if (dataAge < maxDataAge && devicesData.devices.length > 0) {
        console.log(`📊 [CACHE] استخدام البيانات المحفوظة (عمر البيانات: ${Math.round(dataAge/1000)}s)`);
        return;
      }

      lastFetchRef.current = now;

      setDevicesData(prev => ({ ...prev, isLoading: true, error: undefined }));

      // إرسال معلومات المستخدم والجهاز مع الطلب للتتبع الدقيق
      const headers: Record<string, string> = {};

      // محاولة الحصول على المستخدم الحالي من localStorage أو context
      try {
        const authData = localStorage.getItem('auth-storage');
        if (authData) {
          const parsedAuth = JSON.parse(authData);
          const currentUser = parsedAuth?.state?.user?.username;
          if (currentUser) {
            headers['X-Current-User'] = currentUser;
          }
        }
      } catch (e) {
        console.debug('لا يمكن الحصول على معلومات المستخدم من localStorage');
      }

      // إضافة معلومات الجهاز للتتبع الدقيق في الخدمة الموحدة
      headers['X-Device-Hostname'] = window.location.hostname;
      // ✅ استخدام userAgent بدلاً من navigator.platform المهجور
      headers['X-Device-Platform'] = navigator.userAgent.includes('Windows') ? 'Windows' :
                                     navigator.userAgent.includes('Mac') ? 'macOS' :
                                     navigator.userAgent.includes('Linux') ? 'Linux' : 'Unknown';
      headers['X-Device-System'] = navigator.userAgent.includes('Windows') ? 'Windows' :
                                   navigator.userAgent.includes('Mac') ? 'macOS' :
                                   navigator.userAgent.includes('Linux') ? 'Linux' : 'Unknown';
      headers['X-Device-Browser'] = navigator.userAgent.includes('Chrome') ? 'Chrome' :
                                    navigator.userAgent.includes('Firefox') ? 'Firefox' :
                                    navigator.userAgent.includes('Safari') ? 'Safari' : 'Unknown';

      const response = await api.get('/api/settings/connected-devices', { headers });
      const data = response.data;

      if (data.success) {
        // تسجيل مصدر البيانات للتشخيص المحسن
        const dataSource = data.data_source || data.source || 'unknown';
        const hasConsistencyCheck = data.data_consistency_check || false;
        console.log(`🔍 [DEVICES] مصدر البيانات: ${dataSource}, عدد الأجهزة: ${data.devices?.length || 0}, ` +
                   `فحص التناسق: ${hasConsistencyCheck ? '✅' : '❌'}`);

        // معالجة البيانات المحسنة مع التحقق من التناسق
        let processedDevices: ConnectedDevice[] = [];

        if (data.devices && Array.isArray(data.devices)) {
          // إزالة التكرارات مع الحفاظ على أحدث البيانات
          const deviceMap = new Map<string, ConnectedDevice>();

          data.devices.forEach((device: ConnectedDevice) => {
            // التحقق من صحة البيانات الأساسية
            if (!device.device_id || !device.client_ip) {
              console.warn('⚠️ جهاز بدون معرف أو IP:', device);
              return;
            }

            const key = device.client_ip;
            const existing = deviceMap.get(key);

            if (!existing) {
              deviceMap.set(key, device);
            } else {
              // للأجهزة المعتمدة: أولوية خاصة لضمان عدم الاختفاء
              const isDeviceApproved = isApprovedDevice(device);
              const isExistingApproved = isApprovedDevice(existing);

              if (isDeviceApproved && !isExistingApproved) {
                // الجهاز الجديد معتمد والموجود غير معتمد - استخدم الجديد
                deviceMap.set(key, device);
              } else if (!isDeviceApproved && isExistingApproved) {
                // الجهاز الموجود معتمد والجديد غير معتمد - احتفظ بالموجود
                return;
              } else {
                // كلاهما معتمد أو غير معتمد - استخدم المنطق العادي
                const existingTime = new Date(existing.last_access || 0).getTime();
                const currentTime = new Date(device.last_access || 0).getTime();

                // إعطاء أولوية للبيانات الأحدث أو الأكثر اكتمالاً
                if (currentTime > existingTime ||
                    (currentTime === existingTime && device.status === 'online')) {
                  deviceMap.set(key, device);
                }
              }
            }
          });

          processedDevices = Array.from(deviceMap.values());
        }

        // تحديث cache الأجهزة المعتمدة لضمان عدم اختفائها
        processedDevices.forEach(device => {
          if (isApprovedDevice(device)) {
            approvedDevicesCache.current.set(device.device_id, device);
          }
        });

        // دمج الأجهزة المعتمدة من cache إذا لم تكن موجودة في البيانات الحالية
        const currentDeviceIds = new Set(processedDevices.map(d => d.device_id));
        approvedDevicesCache.current.forEach((cachedDevice, deviceId) => {
          if (!currentDeviceIds.has(deviceId)) {
            // الجهاز المعتمد مفقود من البيانات الحالية - أضفه مع تحديث الحالة
            const restoredDevice = {
              ...cachedDevice,
              status: 'recently_active' as const, // تعيين حالة آمنة للأجهزة المعتمدة المستعادة
            };
            processedDevices.push(restoredDevice);
            console.log(`🔄 [CACHE] استعادة الجهاز المعتمد: ${cachedDevice.hostname} (${deviceId})`);
          }
        });

        // ترتيب الأجهزة: الخادم الرئيسي أولاً، ثم حسب الحالة وآخر وصول
        const sortedDevices = processedDevices.sort((a, b) => {
          // الخادم الرئيسي أولاً
          if (a.is_main_server && !b.is_main_server) return -1;
          if (!a.is_main_server && b.is_main_server) return 1;

          // ترتيب حسب الحالة (online > recently_active > offline)
          const statusOrder = { 'online': 0, 'recently_active': 1, 'offline': 2 };
          const statusA = statusOrder[a.status as keyof typeof statusOrder] ?? 3;
          const statusB = statusOrder[b.status as keyof typeof statusOrder] ?? 3;

          if (statusA !== statusB) return statusA - statusB;

          // ترتيب حسب آخر وصول (الأحدث أولاً)
          const timeA = new Date(a.last_access || 0).getTime();
          const timeB = new Date(b.last_access || 0).getTime();
          return timeB - timeA;
        });

        // استخدام الإحصائيات من الخادم إذا كانت متاحة، وإلا احسبها محلياً
        const stats = data.summary ? {
          ...data.summary,
          // التأكد من تطابق العدد الإجمالي
          total_devices: sortedDevices.length
        } : {
          // حساب الإحصائيات محلياً كـ fallback مع تحسينات
          total_devices: sortedDevices.length,
          main_server_count: sortedDevices.filter(d => d.is_main_server).length,
          local_devices_count: sortedDevices.filter(d => d.is_local_access && !d.is_main_server).length,
          remote_devices_count: sortedDevices.filter(d => !d.is_local_access && !d.is_main_server).length,
          online_devices: sortedDevices.filter(d => d.status === 'online').length,
          recently_active_devices: sortedDevices.filter(d => d.status === 'recently_active').length,
          offline_devices: sortedDevices.filter(d => d.status === 'offline').length,
          pending_approval_devices: sortedDevices.filter(d => d.status === 'pending_approval').length,
          blocked_devices_count: 0,
          pending_devices_count: sortedDevices.filter(d => d.approval_status === 'pending').length,
          approved_devices_count: sortedDevices.filter(d => d.is_approved).length,
          advanced_fingerprint_count: sortedDevices.filter(d => d.is_advanced_fingerprint).length,
        };

        // تحديث البيانات مع تسجيل وقت الصحة
        dataValidityRef.current = now;
        setDevicesData({
          devices: sortedDevices,
          summary: stats,
          isLoading: false,
          currentClientIp: data.current_client_ip,
          lastUpdated: new Date(),
        });

        // ✅ إرسال حدث تحديث للمكونات الأخرى
        try {
          window.dispatchEvent(new CustomEvent('devicesUpdated', {
            detail: {
              devices: sortedDevices,
              summary: stats,
              source: 'fetchConnectedDevices',
              timestamp: new Date().toISOString()
            }
          }));
        } catch (eventError) {
          console.debug('خطأ في إرسال حدث تحديث الأجهزة:', eventError);
        }
      } else {
        throw new Error(data.message || 'فشل في جلب بيانات الأجهزة');
      }
    } catch (error: any) {
      // تقليل رسائل الأخطاء في الكونسول
      setDevicesData(prev => ({
        ...prev,
        isLoading: false,
        error: error.response?.data?.message || error.message || 'فشل في جلب بيانات الأجهزة المتصلة',
      }));
    }
  }, []);

  // إعداد WebSocket للتحديث المباشر
  const setupWebSocket = useCallback(() => {
    // عدم الاتصال إذا لم يكن التحديث المباشر مفعل
    if (!useRealTime) {
      console.log('التحديث المباشر معطل، لن يتم الاتصال بـ WebSocket');
      return;
    }

    // تحقق من وجود اتصال نشط
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      console.log('WebSocket متصل بالفعل');
      return;
    }

    // إغلاق أي اتصال سابق بشكل نظيف
    if (wsRef.current) {
      wsRef.current.close(1000, 'Reconnecting');
      wsRef.current = null;
    }

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.hostname}:8002/ws/devices`;

    try {
      // تحقق من دعم WebSocket في المتصفح
      if (typeof WebSocket === 'undefined') {
        console.warn('المتصفح لا يدعم WebSocket');
        setDevicesData(prev => ({
          ...prev,
          error: 'المتصفح لا يدعم WebSocket'
        }));
        return;
      }

      console.log('محاولة الاتصال بـ WebSocket:', wsUrl);
      wsRef.current = new WebSocket(wsUrl);

      // timeout للاتصال
      const connectionTimeout = setTimeout(() => {
        if (wsRef.current && wsRef.current.readyState === WebSocket.CONNECTING) {
          console.warn('انتهت مهلة اتصال WebSocket');
          wsRef.current.close();
          wsRef.current = null;
          // لا نعرض رسالة خطأ للمستخدم
        }
      }, 5000); // تقليل المهلة إلى 5 ثوان

      wsRef.current.onopen = () => {
        clearTimeout(connectionTimeout);
        // إعادة تعيين عداد المحاولات عند النجاح
        reconnectAttemptsRef.current = 0;
        setDevicesData(prev => ({ ...prev, error: undefined }));

        // بدء إرسال heartbeat كل 15 ثانية
        heartbeatIntervalRef.current = setInterval(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({ type: 'heartbeat', timestamp: new Date().toISOString() }));
          }
        }, 15000);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          if (message.type === 'devices_update' && message.data) {
            // معالجة البيانات من WebSocket مع التحقق من التناسق
            const hasConsistencyCheck = message.data.data_consistency_check || false;
            console.log(`🔄 [WebSocket] تحديث الأجهزة، فحص التناسق: ${hasConsistencyCheck ? '✅' : '❌'}`);

            let processedDevices: ConnectedDevice[] = [];

            if (message.data.devices && Array.isArray(message.data.devices)) {
              const deviceMap = new Map<string, ConnectedDevice>();

              message.data.devices.forEach((device: ConnectedDevice) => {
                // التحقق من صحة البيانات
                if (!device.device_id || !device.client_ip) {
                  return;
                }

                const key = device.client_ip;
                const existing = deviceMap.get(key);

                if (!existing) {
                  deviceMap.set(key, device);
                } else {
                  // للأجهزة المعتمدة: أولوية خاصة لضمان عدم الاختفاء
                  const isDeviceApproved = isApprovedDevice(device);
                  const isExistingApproved = isApprovedDevice(existing);

                  if (isDeviceApproved && !isExistingApproved) {
                    // الجهاز الجديد معتمد والموجود غير معتمد - استخدم الجديد
                    deviceMap.set(key, device);
                  } else if (!isDeviceApproved && isExistingApproved) {
                    // الجهاز الموجود معتمد والجديد غير معتمد - احتفظ بالموجود
                    return;
                  } else {
                    // كلاهما معتمد أو غير معتمد - استخدم المنطق العادي
                    const existingTime = new Date(existing.last_access || 0).getTime();
                    const currentTime = new Date(device.last_access || 0).getTime();

                    if (currentTime > existingTime ||
                        (currentTime === existingTime && device.status === 'online')) {
                      deviceMap.set(key, device);
                    }
                  }
                }
              });

              processedDevices = Array.from(deviceMap.values());
            }

            // تحديث cache الأجهزة المعتمدة من WebSocket
            processedDevices.forEach(device => {
              if (isApprovedDevice(device)) {
                approvedDevicesCache.current.set(device.device_id, device);
              }
            });

            // دمج الأجهزة المعتمدة من cache إذا لم تكن موجودة في البيانات الحالية
            const currentDeviceIds = new Set(processedDevices.map(d => d.device_id));
            approvedDevicesCache.current.forEach((cachedDevice, deviceId) => {
              if (!currentDeviceIds.has(deviceId)) {
                // الجهاز المعتمد مفقود من البيانات الحالية - أضفه مع تحديث الحالة
                const restoredDevice = {
                  ...cachedDevice,
                  status: 'recently_active' as const,
                };
                processedDevices.push(restoredDevice);
                console.log(`🔄 [WebSocket-CACHE] استعادة الجهاز المعتمد: ${cachedDevice.hostname} (${deviceId})`);
              }
            });

            // ترتيب الأجهزة: الخادم الرئيسي أولاً، ثم حسب الحالة وآخر وصول
            const sortedDevices = processedDevices.sort((a, b) => {
              // الخادم الرئيسي أولاً
              if (a.is_main_server && !b.is_main_server) return -1;
              if (!a.is_main_server && b.is_main_server) return 1;

              // ترتيب حسب الحالة (online > recently_active > offline)
              const statusOrder = { 'online': 0, 'recently_active': 1, 'offline': 2 };
              const statusA = statusOrder[a.status as keyof typeof statusOrder] ?? 3;
              const statusB = statusOrder[b.status as keyof typeof statusOrder] ?? 3;

              if (statusA !== statusB) return statusA - statusB;

              // ترتيب حسب آخر وصول (الأحدث أولاً)
              const timeA = new Date(a.last_access || 0).getTime();
              const timeB = new Date(b.last_access || 0).getTime();
              return timeB - timeA;
            });

            // استخدام الإحصائيات من الخادم أو حسابها محلياً
            const stats = message.data.summary ? {
              ...message.data.summary,
              total_devices: sortedDevices.length
            } : {
              total_devices: sortedDevices.length,
              main_server_count: sortedDevices.filter(d => d.is_main_server).length,
              local_devices_count: sortedDevices.filter(d => d.is_local_access && !d.is_main_server).length,
              remote_devices_count: sortedDevices.filter(d => !d.is_local_access && !d.is_main_server).length,
              online_devices: sortedDevices.filter(d => d.status === 'online').length,
              recently_active_devices: sortedDevices.filter(d => d.status === 'recently_active').length,
              offline_devices: sortedDevices.filter(d => d.status === 'offline').length,
              pending_approval_devices: sortedDevices.filter(d => d.status === 'pending_approval').length,
              blocked_devices_count: 0,
              pending_devices_count: sortedDevices.filter(d => d.approval_status === 'pending').length,
              approved_devices_count: sortedDevices.filter(d => d.is_approved).length,
              advanced_fingerprint_count: sortedDevices.filter(d => d.is_advanced_fingerprint).length,
            };

            // تحديث ذكي مع استقرار خاص للخادم الرئيسي وفحص صحة البيانات
            setDevicesData(prevData => {
              // تحديث وقت صحة البيانات
              dataValidityRef.current = Date.now();

              // فحص التغييرات المهمة مع تجاهل التغييرات الطفيفة للخادم الرئيسي
              const hasSignificantChanges = (
                !prevData.devices ||
                prevData.devices.length !== sortedDevices.length ||
                JSON.stringify(prevData.summary) !== JSON.stringify(stats) ||
                sortedDevices.some((device, index) => {
                  const prevDevice = prevData.devices[index];
                  if (!prevDevice) return true;

                  // للخادم الرئيسي: تجاهل التغييرات الطفيفة في المستخدم إذا كان التغيير سريع
                  if (device.is_main_server && prevDevice.is_main_server) {
                    // فحص التغييرات المهمة فقط للخادم الرئيسي
                    return device.status !== prevDevice.status ||
                           device.device_id !== prevDevice.device_id ||
                           device.client_ip !== prevDevice.client_ip;
                  }

                  // للأجهزة الأخرى: فحص جميع التغييرات
                  return device.current_user !== prevDevice.current_user ||
                         device.status !== prevDevice.status ||
                         device.device_id !== prevDevice.device_id ||
                         device.approval_status !== prevDevice.approval_status;
                })
              );

              // تحديث فقط إذا كانت هناك تغييرات مهمة
              if (hasSignificantChanges) {
                // للخادم الرئيسي: الحفاظ على المستخدم السابق إذا كان التغيير سريع
                const stabilizedDevices = sortedDevices.map((device, index) => {
                  const prevDevice = prevData.devices?.[index];

                  if (device.is_main_server && prevDevice?.is_main_server) {
                    // إذا كان المستخدم تغير من قيمة إلى null أو العكس بسرعة، احتفظ بالقيمة السابقة
                    const timeDiff = new Date().getTime() - (prevData.lastUpdated?.getTime() || 0);
                    if (timeDiff < 2000 && // أقل من ثانيتين
                        ((device.current_user && !prevDevice.current_user) ||
                         (!device.current_user && prevDevice.current_user))) {
                      return {
                        ...device,
                        current_user: prevDevice.current_user // الحفاظ على المستخدم السابق
                      };
                    }
                  }

                  return device;
                });

                // ✅ إرسال حدث تحديث للمكونات الأخرى
                try {
                  window.dispatchEvent(new CustomEvent('devicesUpdated', {
                    detail: {
                      devices: stabilizedDevices,
                      summary: stats,
                      source: 'websocket',
                      timestamp: new Date().toISOString()
                    }
                  }));
                } catch (eventError) {
                  console.debug('خطأ في إرسال حدث تحديث WebSocket:', eventError);
                }

                return {
                  devices: stabilizedDevices,
                  summary: stats,
                  isLoading: false,
                  lastUpdated: new Date(),
                };
              }

              // إرجاع البيانات السابقة بدون تغيير لتجنب إعادة الرسم
              return {
                ...prevData,
                lastUpdated: new Date(), // تحديث الوقت فقط
              };
            });
          }
        } catch (error) {
          // تجاهل أخطاء parsing لتقليل الضوضاء
        }
      };

      wsRef.current.onclose = (event) => {
        // تسجيل صامت فقط إذا كان التحديث المباشر مفعل
        if (useRealTime) {
          console.log('WebSocket مغلق، الكود:', event.code, 'السبب:', event.reason);
        }
        clearTimeout(connectionTimeout);

        // إيقاف heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }

        // تنظيف المرجع
        wsRef.current = null;

        // إعادة الاتصال فقط إذا كان التحديث المباشر مفعل ولم نتجاوز الحد الأقصى
        if (useRealTime &&
            reconnectAttemptsRef.current < maxReconnectAttempts &&
            event.code !== 1000 &&
            event.code !== 1001 &&
            event.code !== 1006) { // تجنب إعادة الاتصال عند فشل الاتصال الأولي

          reconnectAttemptsRef.current += 1;
          const delay = Math.min(5000 * reconnectAttemptsRef.current, 30000); // زيادة التأخير

          if (useRealTime) { // فحص مزدوج للتأكد
            console.log(`محاولة إعادة الاتصال ${reconnectAttemptsRef.current}/${maxReconnectAttempts} بعد ${delay}ms`);

            reconnectTimeoutRef.current = setTimeout(() => {
              if (useRealTime) { // فحص ثالث قبل إعادة الاتصال
                setupWebSocket();
              }
            }, delay);
          }
        } else {
          if (useRealTime) {
            console.log('توقف عن محاولة إعادة الاتصال بـ WebSocket');
          }
          // إعادة تعيين العداد
          reconnectAttemptsRef.current = 0;
        }
      };

      wsRef.current.onerror = (error) => {
        // تسجيل صامت للخطأ فقط إذا كان التحديث المباشر مفعل
        if (useRealTime) {
          console.warn('خطأ في WebSocket (التحديث المباشر مفعل):', error);
        }
        clearTimeout(connectionTimeout);

        // عدم إظهار أي رسائل خطأ للمستخدم
        // النظام سيعمل بالتحديث العادي

        // إغلاق الاتصال بشكل نظيف
        if (wsRef.current) {
          wsRef.current.close(1000, 'Error occurred');
          wsRef.current = null;
        }
      };

      // تم نقل heartbeat إلى onopen

    } catch (error) {
      // fallback إلى التحديث العادي بدون رسائل كونسول
      fetchConnectedDevices();
    }
  }, [useRealTime, fetchConnectedDevices]);

  // تنظيف WebSocket عند تغيير useRealTime
  useEffect(() => {
    if (!useRealTime) {
      // إغلاق WebSocket إذا كان مفتوح
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }
      // إعادة تعيين عداد المحاولات
      reconnectAttemptsRef.current = 0;
    }
  }, [useRealTime]);

  // جلب البيانات الأولية وإعداد WebSocket أو التحديث الدوري
  useEffect(() => {
    fetchConnectedDevices();

    let interval: NodeJS.Timeout | null = null;

    if (useRealTime) {
      console.log('تفعيل التحديث المباشر - بدء اتصال WebSocket');
      setupWebSocket();
    } else {
      console.log('التحديث المباشر معطل - استخدام التحديث الدوري');

      // إغلاق أي اتصال WebSocket موجود
      if (wsRef.current) {
        console.log('إغلاق WebSocket بسبب تعطيل التحديث المباشر');
        wsRef.current.close(1000, 'Real-time disabled');
        wsRef.current = null;
      }

      // تنظيف المؤقتات
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }

      // إعادة تعيين العداد
      reconnectAttemptsRef.current = 0;

      // تعطيل التحديث الدوري لتوفير موارد النظام
      // المستخدم يمكنه تحديث البيانات يدوياً عند الحاجة
      // interval = setInterval(() => {
      //   fetchConnectedDevices();
      // }, 30000); // 30 ثانية بدلاً من 5 ثوان
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [fetchConnectedDevices, setupWebSocket, useRealTime]);

  // مستمع للأحداث المخصصة للتحديث الفوري
  useEffect(() => {
    const handleDeviceUserUpdate = (event: CustomEvent) => {
      console.log('🔄 تم استلام حدث تحديث المستخدم:', event.detail);

      // تحديث فوري للبيانات بعد تأخير قصير للسماح للخادم بالمعالجة
      if (forceUpdateTimeoutRef.current) {
        clearTimeout(forceUpdateTimeoutRef.current);
      }

      forceUpdateTimeoutRef.current = setTimeout(() => {
        console.log('🔄 تحديث فوري للأجهزة بعد تغيير المستخدم');
        fetchConnectedDevices();
      }, 500); // تأخير نصف ثانية
    };

    // إضافة مستمع للحدث المخصص
    window.addEventListener('deviceUserUpdated', handleDeviceUserUpdate as EventListener);

    return () => {
      // إزالة مستمع الحدث
      window.removeEventListener('deviceUserUpdated', handleDeviceUserUpdate as EventListener);

      // تنظيف المؤقت
      if (forceUpdateTimeoutRef.current) {
        clearTimeout(forceUpdateTimeoutRef.current);
        forceUpdateTimeoutRef.current = null;
      }
    };
  }, [fetchConnectedDevices]);

  // تنظيف عام عند إلغاء تحميل المكون
  useEffect(() => {
    return () => {
      // إغلاق WebSocket بشكل نظيف
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmounting'); // إغلاق طبيعي
        wsRef.current = null;
      }

      // تنظيف جميع المؤقتات
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }

      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }

      if (forceUpdateTimeoutRef.current) {
        clearTimeout(forceUpdateTimeoutRef.current);
        forceUpdateTimeoutRef.current = null;
      }

      // إعادة تعيين عداد المحاولات
      reconnectAttemptsRef.current = 0;
    };
  }, []);

  return {
    ...devicesData,
    refreshDevices: fetchConnectedDevices,
  };
};

/**
 * Hook مبسط للحصول على ملخص الأجهزة فقط
 */
export const useDevicesSummary = () => {
  const { summary, isLoading, error, refreshDevices } = useConnectedDevices(false);

  return {
    summary,
    isLoading,
    error,
    refreshSummary: refreshDevices,
  };
};
